<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气泡图测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>气泡图点击测试</h1>
        
        <div class="test-info">
            <h3>测试说明：</h3>
            <ul>
                <li>单击气泡：显示详细信息</li>
                <li>双击气泡：执行钻取操作</li>
                <li>查看下方日志了解点击事件是否触发</li>
            </ul>
        </div>

        <div class="chart-container">
            <canvas id="testBubbleChart"></canvas>
        </div>

        <div class="log" id="logContainer">
            <div>日志输出：</div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${time}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        // 测试数据
        const testData = [
            { x: 80, y: 15, r: 20, label: '张东明', sales: 398000, achievement: 151.4, growth: 15.9 },
            { x: 60, y: -20, r: 15, label: '杨宗祥', sales: 250000, achievement: 95.2, growth: -20.1 },
            { x: 100, y: 25, r: 18, label: '陈洁', sales: 320000, achievement: 128.5, growth: 25.3 },
            { x: 90, y: -10, r: 12, label: '刘美丽', sales: 180000, achievement: 87.3, growth: -10.2 }
        ];

        log('开始创建测试气泡图...');

        // 创建气泡图
        const ctx = document.getElementById('testBubbleChart').getContext('2d');
        
        let lastClickTime = 0;
        
        const chart = new Chart(ctx, {
            type: 'bubble',
            data: {
                datasets: [{
                    label: '人员表现测试',
                    data: testData,
                    backgroundColor: testData.map(item => 
                        item.growth >= 0 ? 'rgba(34, 197, 94, 0.6)' : 'rgba(239, 68, 68, 0.6)'
                    ),
                    borderColor: testData.map(item => 
                        item.growth >= 0 ? 'rgba(34, 197, 94, 1)' : 'rgba(239, 68, 68, 1)'
                    ),
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '人员表现气泡图测试'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const data = context.raw;
                                return `${data.label}: ¥${(data.sales / 10000).toFixed(1)}万, 达成率: ${data.achievement}%, 增长率: ${data.growth}%`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '达成率 (%)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '同比增长率 (%)'
                        }
                    }
                },
                onClick: (event, elements) => {
                    log('🖱️ 气泡图点击事件触发');
                    log(`点击的元素数量: ${elements.length}`);
                    
                    if (elements.length > 0) {
                        const element = elements[0];
                        const dataIndex = element.index;
                        const data = testData[dataIndex];
                        
                        log(`点击的气泡: ${data.label}`);
                        
                        // 检测双击
                        const now = Date.now();
                        const timeDiff = now - lastClickTime;
                        
                        log(`点击时间间隔: ${timeDiff}ms`);
                        
                        if (lastClickTime && timeDiff < 500) {
                            log(`🚀 检测到双击 - ${data.label}`);
                            alert(`双击钻取: ${data.label}\n销售金额: ¥${(data.sales / 10000).toFixed(1)}万\n达成率: ${data.achievement}%\n增长率: ${data.growth}%`);
                        } else {
                            log(`⏱️ 单击事件 - ${data.label}`);
                            // 延迟显示单击信息，避免双击时显示
                            setTimeout(() => {
                                if (Date.now() - lastClickTime >= 500) {
                                    alert(`单击详情: ${data.label}\n销售金额: ¥${(data.sales / 10000).toFixed(1)}万\n达成率: ${data.achievement}%\n增长率: ${data.growth}%`);
                                }
                            }, 500);
                        }
                        
                        lastClickTime = now;
                    } else {
                        log('❌ 没有点击到任何气泡元素');
                    }
                },
                onHover: (event, elements) => {
                    event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    if (elements.length > 0) {
                        const data = testData[elements[0].index];
                        log(`🖱️ 鼠标悬停: ${data.label}`);
                    }
                }
            }
        });

        log('✅ 测试气泡图创建完成');
        log('请尝试点击气泡进行测试...');
    </script>
</body>
</html>
